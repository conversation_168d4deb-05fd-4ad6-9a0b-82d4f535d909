package com.airdoc.mpd.detection

import android.content.Context
import com.airdoc.component.common.download.DownloadUtils
import com.airdoc.component.common.download.IHttpListener
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.detection.bean.MaterialLibraryDto
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Request
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.zip.ZipInputStream

/**
 * FileName: MaterialDownloader
 * Author by <PERSON><PERSON><PERSON><PERSON><PERSON>,Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 素材库下载器 - 负责下载和解压素材库
 */
class MaterialDownloader(
    private val context: Context,
    private val callback: MaterialDownloadCallback
) {

    companion object {
        private const val TAG = "MaterialDownloader"
        private const val TEMP_DIR = "temp_materials"
    }

    interface MaterialDownloadCallback {
        fun onDownloadStart(materialLibrary: MaterialLibraryDto)
        fun onDownloadProgress(progress: Float, total: Long)
        fun onDownloadComplete(materialLibrary: MaterialLibraryDto)
        fun onExtractStart(materialLibrary: MaterialLibraryDto)
        fun onExtractComplete(materialLibrary: MaterialLibraryDto, extractedFileCount: Int)
        fun onError(materialLibrary: MaterialLibraryDto, error: String)
    }

    /**
     * 下载并解压素材库
     */
    suspend fun downloadAndExtractMaterial(materialLibrary: MaterialLibraryDto) {
        withContext(Dispatchers.IO) {
            try {
                val zipUrl = materialLibrary.zipUrl
                if (zipUrl.isNullOrEmpty()) {
                    callback.onError(materialLibrary, "下载地址为空")
                    return@withContext
                }

                val materialId = materialLibrary.id ?: 0L
                if (materialId == 0L) {
                    callback.onError(materialLibrary, "素材库ID无效")
                    return@withContext
                }

                // 创建临时目录
                val tempDir = File(context.cacheDir, TEMP_DIR)
                if (!tempDir.exists()) {
                    tempDir.mkdirs()
                }

                // 生成临时文件名
                val tempFileName = "material_${materialId}_${System.currentTimeMillis()}.zip"
                val tempFile = File(tempDir, tempFileName)

                Logger.d(TAG, msg = "开始下载素材库: ${materialLibrary.name}, URL: $zipUrl")
                callback.onDownloadStart(materialLibrary)

                // 下载文件
                downloadFile(zipUrl, tempFile, materialLibrary)

            } catch (e: Exception) {
                Logger.e(TAG, msg = "下载素材库失败: ${e.message}")
                callback.onError(materialLibrary, "下载失败: ${e.message}")
            }
        }
    }

    /**
     * 下载文件
     */
    private fun downloadFile(url: String, targetFile: File, materialLibrary: MaterialLibraryDto) {
        DownloadUtils.download(
            url,
            targetFile.parent ?: "",
            targetFile.name,
            object : IHttpListener.DownloadCallback {
                override fun onPreDownload(request: Request, id: Int) {
                    Logger.d(TAG, msg = "开始下载: ${materialLibrary.name}")
                }

                override fun onAfterDownload(id: Int) {
                    Logger.d(TAG, msg = "下载完成: ${materialLibrary.name}")
                }

                override fun onProgressUpdate(progress: Float, total: Long) {
                    callback.onDownloadProgress(progress, total)
                }

                override fun onPostDownload(file: File?) {
                    if (file != null && file.exists()) {
                        // 验证文件完整性
                        val expectedChecksum = materialLibrary.checksum
                        if (!expectedChecksum.isNullOrEmpty()) {
                            if (!MaterialManager.verifyFileIntegrity(file, expectedChecksum)) {
                                callback.onError(materialLibrary, "文件校验失败")
                                file.delete()
                                return
                            }
                        }

                        callback.onDownloadComplete(materialLibrary)
                        
                        // 开始解压
                        extractZipFile(file, materialLibrary)
                    } else {
                        callback.onError(materialLibrary, "下载的文件不存在")
                    }
                }

                override fun onErrorDownload(error: String) {
                    Logger.e(TAG, msg = "下载失败: $error")
                    callback.onError(materialLibrary, "下载失败: $error")
                }
            }
        )
    }

    /**
     * 解压ZIP文件
     */
    private fun extractZipFile(zipFile: File, materialLibrary: MaterialLibraryDto) {
        try {
            val materialId = materialLibrary.id ?: 0L
            val targetDir = MaterialManager.getMaterialDir(context, materialId)
            
            // 清空目标目录
            if (targetDir.exists()) {
                targetDir.deleteRecursively()
            }
            targetDir.mkdirs()

            Logger.d(TAG, msg = "开始解压素材库: ${materialLibrary.name} 到 ${targetDir.absolutePath}")
            callback.onExtractStart(materialLibrary)

            var extractedFileCount = 0
            
            ZipInputStream(FileInputStream(zipFile)).use { zipInputStream ->
                var entry = zipInputStream.nextEntry
                
                while (entry != null) {
                    if (!entry.isDirectory) {
                        val entryFile = File(targetDir, entry.name)
                        
                        // 确保父目录存在
                        entryFile.parentFile?.mkdirs()
                        
                        // 解压文件
                        FileOutputStream(entryFile).use { outputStream ->
                            zipInputStream.copyTo(outputStream)
                        }
                        
                        extractedFileCount++
                        Logger.d(TAG, msg = "解压文件: ${entry.name}")
                    }
                    
                    zipInputStream.closeEntry()
                    entry = zipInputStream.nextEntry
                }
            }

            // 删除临时ZIP文件
            zipFile.delete()

            // 更新素材库映射
            MaterialManager.addOrUpdateMaterial(
                context, 
                materialLibrary, 
                targetDir.absolutePath, 
                extractedFileCount
            )

            Logger.d(TAG, msg = "解压完成: ${materialLibrary.name}, 共解压 $extractedFileCount 个文件")
            callback.onExtractComplete(materialLibrary, extractedFileCount)

        } catch (e: Exception) {
            Logger.e(TAG, msg = "解压失败: ${e.message}")
            callback.onError(materialLibrary, "解压失败: ${e.message}")
            
            // 清理失败的文件
            zipFile.delete()
        }
    }

    /**
     * 取消下载
     */
    fun cancelDownload() {
        // 这里可以添加取消下载的逻辑
        // DownloadUtils 可能需要提供取消下载的方法
    }
}
