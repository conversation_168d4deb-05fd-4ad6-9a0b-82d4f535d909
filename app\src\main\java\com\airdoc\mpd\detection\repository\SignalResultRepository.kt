package com.airdoc.mpd.detection.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.mpd.detection.api.SignalResultApiService
import com.airdoc.mpd.detection.bean.SignalResult
import com.airdoc.mpd.net.MpdRetrofitClient

/**
 * 信号结果数据仓库
 */
class SignalResultRepository : BaseRepository() {

    /**
     * 上传信号结果数据
     * @param signalResult 信号结果数据
     */
    suspend fun uploadSignalResult(signalResult: SignalResult): ApiResponse<Any> {
        return executeHttp {
            MpdRetrofitClient.createService(SignalResultApiService::class.java).uploadSignalResult(signalResult)
        }
    }

    /**
     * 批量上传信号结果数据
     * @param signalResults 信号结果数据列表
     */
    suspend fun uploadSignalResults(signalResults: List<SignalResult>): ApiResponse<Any> {
        return executeHttp {
            MpdRetrofitClient.createService(SignalResultApiService::class.java).uploadSignalResults(signalResults)
        }
    }
}