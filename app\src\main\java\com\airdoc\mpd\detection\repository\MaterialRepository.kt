package com.airdoc.mpd.detection.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.mpd.detection.api.MaterialApiService
import com.airdoc.mpd.detection.bean.MaterialLibraryDto
import com.airdoc.mpd.net.MpdRetrofitClient

/**
 * FileName: MaterialRepository
 * Author by <PERSON><PERSON><PERSON><PERSON><PERSON>,Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 素材库数据仓库
 */
class MaterialRepository : BaseRepository() {

    /**
     * 获取有效素材库列表
     */
    suspend fun listMaterialLibraries(): ApiResponse<List<MaterialLibraryDto>> {
        return executeHttp {
            MpdRetrofitClient.createService(MaterialApiService::class.java).listMaterialLibraries()
        }
    }
}
