package com.airdoc.mpd.detection.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.mpd.detection.bean.SignalResult
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * 信号结果上传API服务
 */
interface SignalResultApiService {

    /**
     * 上传信号结果数据
     * @param signalResult 信号结果数据
     */
    @POST("api/signal/upload")
    suspend fun uploadSignalResult(@Body signalResult: SignalResult) : ApiResponse<Any>

    /**
     * 批量上传信号结果数据
     * @param signalResults 信号结果数据列表
     */
    @POST("api/signal/upload/batch")
    suspend fun uploadSignalResults(@Body signalResults: List<SignalResult>) : ApiResponse<Any>
}