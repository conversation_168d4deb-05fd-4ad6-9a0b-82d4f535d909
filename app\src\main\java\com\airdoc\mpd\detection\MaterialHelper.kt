package com.airdoc.mpd.detection

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.airdoc.component.common.log.Logger
import java.io.File

/**
 * FileName: MaterialHelper
 * Author by <PERSON><PERSON><PERSON><PERSON><PERSON>,Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 素材库辅助工具类 - 提供前端访问素材库资源的便捷方法
 */
object MaterialHelper {

    private const val TAG = "MaterialHelper"

    /**
     * 素材库图片信息
     */
    data class MaterialImage(
        val fileName: String,
        val filePath: String,
        val fileSize: Long,
        val lastModified: Long
    )

    /**
     * 获取指定素材库的所有图片信息
     */
    fun getMaterialImages(context: Context, materialId: Long): List<MaterialImage> {
        return try {
            val images = MaterialManager.getMaterialImages(context, materialId)
            images.map { file ->
                MaterialImage(
                    fileName = file.name,
                    filePath = file.absolutePath,
                    fileSize = file.length(),
                    lastModified = file.lastModified()
                )
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取素材库图片失败: ${e.message}")
            emptyList()
        }
    }

    /**
     * 获取指定素材库的图片文件路径列表
     */
    fun getMaterialImagePaths(context: Context, materialId: Long): List<String> {
        return getMaterialImages(context, materialId).map { it.filePath }
    }

    /**
     * 获取指定素材库的图片文件名列表
     */
    fun getMaterialImageNames(context: Context, materialId: Long): List<String> {
        return getMaterialImages(context, materialId).map { it.fileName }
    }

    /**
     * 根据文件名获取素材库图片文件
     */
    fun getMaterialImageByName(context: Context, materialId: Long, fileName: String): File? {
        return try {
            val materialDir = MaterialManager.getMaterialDir(context, materialId)
            val imageFile = File(materialDir, fileName)
            if (imageFile.exists() && imageFile.isFile) imageFile else null
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取素材库图片文件失败: ${e.message}")
            null
        }
    }

    /**
     * 加载素材库图片为Bitmap
     */
    fun loadMaterialImageBitmap(context: Context, materialId: Long, fileName: String): Bitmap? {
        return try {
            val imageFile = getMaterialImageByName(context, materialId, fileName)
            if (imageFile != null) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "加载素材库图片失败: ${e.message}")
            null
        }
    }

    /**
     * 加载素材库图片为Bitmap（带缩放）
     */
    fun loadMaterialImageBitmap(
        context: Context, 
        materialId: Long, 
        fileName: String, 
        maxWidth: Int, 
        maxHeight: Int
    ): Bitmap? {
        return try {
            val imageFile = getMaterialImageByName(context, materialId, fileName)
            if (imageFile != null) {
                // 先获取图片尺寸
                val options = BitmapFactory.Options()
                options.inJustDecodeBounds = true
                BitmapFactory.decodeFile(imageFile.absolutePath, options)
                
                // 计算缩放比例
                val imageWidth = options.outWidth
                val imageHeight = options.outHeight
                var inSampleSize = 1
                
                if (imageHeight > maxHeight || imageWidth > maxWidth) {
                    val halfHeight = imageHeight / 2
                    val halfWidth = imageWidth / 2
                    
                    while ((halfHeight / inSampleSize) >= maxHeight && (halfWidth / inSampleSize) >= maxWidth) {
                        inSampleSize *= 2
                    }
                }
                
                // 加载缩放后的图片
                options.inSampleSize = inSampleSize
                options.inJustDecodeBounds = false
                BitmapFactory.decodeFile(imageFile.absolutePath, options)
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "加载缩放素材库图片失败: ${e.message}")
            null
        }
    }

    /**
     * 检查素材库是否存在
     */
    fun isMaterialExists(context: Context, materialId: Long): Boolean {
        return MaterialManager.isMaterialExists(context, materialId)
    }

    /**
     * 获取素材库信息
     */
    fun getMaterialInfo(materialId: Long): MaterialManager.MaterialInfo? {
        return MaterialManager.getMaterialInfo(materialId)
    }

    /**
     * 获取所有本地素材库信息
     */
    fun getAllMaterialInfo(): List<MaterialManager.MaterialInfo> {
        return MaterialManager.getAllMaterialInfo()
    }

    /**
     * 获取素材库目录路径
     */
    fun getMaterialDirPath(context: Context, materialId: Long): String {
        return MaterialManager.getMaterialDir(context, materialId).absolutePath
    }

    /**
     * 获取素材库图片数量
     */
    fun getMaterialImageCount(context: Context, materialId: Long): Int {
        return MaterialManager.getMaterialImages(context, materialId).size
    }

    /**
     * 删除素材库
     */
    fun deleteMaterial(context: Context, materialId: Long): Boolean {
        return MaterialManager.deleteMaterial(context, materialId)
    }

    /**
     * 获取素材库总大小（字节）
     */
    fun getMaterialSize(context: Context, materialId: Long): Long {
        return try {
            val materialDir = MaterialManager.getMaterialDir(context, materialId)
            calculateDirectorySize(materialDir)
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算素材库大小失败: ${e.message}")
            0L
        }
    }

    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        if (directory.exists()) {
            directory.listFiles()?.forEach { file ->
                size += if (file.isDirectory) {
                    calculateDirectorySize(file)
                } else {
                    file.length()
                }
            }
        }
        return size
    }

    /**
     * 格式化文件大小
     */
    fun formatFileSize(sizeInBytes: Long): String {
        return when {
            sizeInBytes < 1024 -> "${sizeInBytes}B"
            sizeInBytes < 1024 * 1024 -> "${sizeInBytes / 1024}KB"
            sizeInBytes < 1024 * 1024 * 1024 -> "${sizeInBytes / 1024 / 1024}MB"
            else -> "${sizeInBytes / 1024 / 1024 / 1024}GB"
        }
    }
}
