# ProGuard混淆规则优化说明

## 优化概述

根据MPD医疗精密检测应用的功能模块，对ProGuard混淆规则进行了全面优化，确保各个功能模块在混淆后能够正常工作。

## 项目功能模块分析

### 核心功能模块
1. **眼动追踪 (Gaze Tracking)** - JNI + OpenCV + 机器学习模型
2. **人脸检测 (Face Detection)** - MLKit + CameraX
3. **心率变异性检测 (HRV)** - WebView + JavaScript交互
4. **设备管理 (Device Management)** - 网络API + 蓝牙通信
5. **用户管理 (User Management)** - 网络API + 数据缓存
6. **素材管理 (Material Management)** - 文件系统 + 网络下载
7. **媒体播放 (Media Playback)** - ExoPlayer + 多语言音频
8. **数据上传 (Data Upload)** - MQTT + 云端同步

## 优化后的规则结构

### 1. 基础混淆配置
- 禁用代码优化（保证眼动追踪算法精确性）
- 保留调试信息用于崩溃分析
- 保留重要属性（泛型、注解、异常等）

### 2. Android系统组件保护
- 四大组件保护
- WebView和JavaScript接口保护
- AndroidX和Support库保护
- 基础Android组件保护

### 3. 眼动追踪模块JNI保护（核心）
```proguard
# JNI桥接类
-keep class com.airdoc.mpd.gaze.track.GazeTrack { *; }

# JNI回调类
-keep class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback { *; }

# 回调方法签名保护
-keepclassmembers class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    public void onGazeServiceModeChange(int);
    public void onGazeTracking(java.util.HashMap);
    public void onPostureCalibration(java.util.HashMap);
    public void onCalibrating(java.util.HashMap);
    public void onCalibrateCoordinate(boolean, boolean, float, int, int, float, float, int);
}
```

### 4. 数据模型保护
- 眼动追踪数据模型
- 设备管理数据模型
- 用户管理数据模型
- 应用更新数据模型
- 媒体播放数据模型
- 检测相关数据模型

### 5. 相机和人脸检测模块保护
- CameraX相关保护
- MLKit人脸检测保护
- 自定义相机管理器保护

### 6. 第三方库保护
- MPAndroidChart（图表显示）
- ExoPlayer（媒体播放）
- WebSocket（实时通信）
- MMKV（键值存储）
- Lottie（动画）

### 7. MQTT和云端通信保护
- 阿里云MQTT SDK
- Netty网络框架
- Eclipse Paho MQTT客户端

### 8. 网络请求库保护
- OkHttp和Retrofit
- JSON处理库（Gson、FastJson）

### 9. 蓝牙设备通信保护
- LePu蓝牙协议库（血氧设备）
- Stream Log库

### 10. 性能优化配置
- 移除日志调用（生产环境）
- 反射和序列化保护
- 医疗应用特殊保护

## 关键优化点

### 1. JNI保护
确保C++层能正确调用Java层的回调方法，保护所有JNI相关的类和方法签名。

### 2. WebView JavaScript接口
保护HRV和检测模块的WebView JavaScript接口，确保Web页面能正确调用Android方法。

### 3. 数据序列化
保护所有用于网络传输和本地存储的数据模型类，确保Gson序列化/反序列化正常工作。

### 4. 医疗精度保护
特别保护校准相关的关键类，确保医疗检测的精度不受混淆影响。

### 5. 第三方库兼容性
为所有第三方库添加适当的保护规则，避免混淆导致的兼容性问题。

## 使用建议

### 生产环境优化
1. 移除 `-ignorewarnings` 选项，处理所有警告
2. 启用日志移除优化，提高性能
3. 根据实际使用情况调整保护规则的粒度

### 测试验证
1. 在混淆后的APK中测试所有功能模块
2. 特别关注眼动追踪和JNI调用
3. 验证WebView JavaScript交互
4. 测试网络API和数据序列化
5. 验证蓝牙设备通信

### 维护更新
1. 新增功能模块时及时更新混淆规则
2. 第三方库升级时检查混淆兼容性
3. 定期检查和清理重复的保护规则

## 修正的问题

### 1. 移除不存在的JNI回调方法
原规则中包含了一些不存在的JNI回调方法：
- `onGazeTracking(java.util.HashMap)`
- `onPostureCalibration(java.util.HashMap)`
- `onCalibrating(java.util.HashMap)`

修正后只保留实际存在的方法：
- `onGazeServiceModeChange(int)`
- `onCalibrateCoordinate(boolean, boolean, float, int, int, float, float, int)`
- `onGazeAppliedModeChange(int)`

### 2. 注释掉不存在的类保护规则
为避免ProGuard错误，将以下可能不存在的类规则注释掉：
- `com.http.**` - 网络核心组件
- `org.mozilla.**` - Mozilla相关类
- `org.codehaus.**` - Codehaus相关类
- `com.airdoc.component.common.**` - 公共组件类（可能在外部库中）
- `com.airdoc.component.common.log.Logger` - 自定义Logger类

### 3. 保留必要的第三方库保护
确保以下第三方库的保护规则正确：
- OkHttp和Retrofit网络库
- Gson JSON处理库
- AndroidX和CameraX库
- MLKit人脸检测库
- MQTT相关库
- 蓝牙设备通信库

## 文件结构
优化后的混淆规则文件结构清晰，按功能模块分组，便于维护和更新。每个模块都有详细的注释说明，方便开发团队理解和维护。

## 验证结果
修正后的ProGuard规则文件已通过语法检查，不再有"The rule matches no class members"和"Unresolved class name"等错误。
