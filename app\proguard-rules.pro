# ===============================================================================
# MPD医疗精密检测应用 - ProGuard混淆规则配置
#
# 项目功能模块：
# - 眼动追踪 (Gaze Tracking) - JNI + OpenCV + 机器学习模型
# - 人脸检测 (Face Detection) - MLKit + CameraX
# - 心率变异性检测 (HRV) - WebView + JavaScript交互
# - 设备管理 (Device Management) - 网络API + 蓝牙通信
# - 用户管理 (User Management) - 网络API + 数据缓存
# - 素材管理 (Material Management) - 文件系统 + 网络下载
# - 媒体播放 (Media Playback) - ExoPlayer + 多语言音频
# - 数据上传 (Data Upload) - MQTT + 云端同步
# ===============================================================================

# ===============================================================================
# 基础混淆配置
# ===============================================================================

# 禁止生成混合大小写的类名
-dontusemixedcaseclassnames
# 指定不去忽略非公共库的类
-dontskipnonpubliclibraryclasses
# 忽略警告信息（医疗应用需要严格控制，生产环境建议移除此选项）
-ignorewarnings
# 在混淆过程中输出详细的日志信息
-verbose

# 禁用代码优化阶段（保证眼动追踪算法的精确性）
-dontoptimize
# 禁用预验证阶段,Android不需要preverify,去掉这一步能够加快混淆速度
-dontpreverify

# 保留调试信息用于崩溃分析
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# 保留重要属性
-keepattributes Signature          # 避免混淆泛型
-keepattributes *Annotation*       # 保留注解
-keepattributes Exceptions         # 保留异常信息
-keepattributes InnerClasses       # 保留内部类信息
-keepattributes EnclosingMethod    # 保留匿名内部类的外部方法信息

# ===============================================================================
# Android系统组件保护
# ===============================================================================

# 保留四大组件
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# ===============================================================================
# WebView相关保护 (HRV心率变异性检测模块使用)
# ===============================================================================

# WebView基础保护
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebChromeClient {
    public void *(android.webkit.WebView, java.lang.String);
}

# JavaScript接口保护 - HRV和检测模块的WebView交互
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保护HRV WebView的JavaScript接口
-keep class com.airdoc.mpd.detection.hrv.HrvWebView$HrvAction {
    public *;
}
-keep class com.airdoc.mpd.detection.DetectionWebView$DetectionAction {
    public *;
}

# ===============================================================================
# AndroidX和Support库保护
# ===============================================================================

# 保留support下的所有类及其内部类（兼容旧版本）
-keep class android.support.** {*;}
-keep interface android.support.** {*;}
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**
-dontwarn android.support.**

# 保留androidx下的所有类及其内部类
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-keep class com.google.android.material.** {*;}
-dontwarn androidx.**
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**

# ===============================================================================
# Android基础组件保护
# ===============================================================================

# 保留R资源类
-keep class **.R$* {*;}

# 保留所有native方法（眼动追踪JNI层需要）
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留Activity中的onClick方法
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

# 保留枚举类的values()和valueOf()方法
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留Parcelable实现类的CREATOR字段
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 这指定了继承Serizalizable的类的如下成员不被移除混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#表示不混淆任何一个View中的setXxx()和getXxx()方法，因为属性动画需要有相应的setter和getter的方法实现，混淆了就无法工作了。
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Behavior是通过反射调用的，需要保留
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
   *;
}

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
#-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

##---------------End: proguard configuration for Gson  ----------

# Okhttp3的混淆配置
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*
# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# OkHttp3
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-keep class okhttp3.** { *;}
-dontwarn okio.**

# Okio
-dontwarn com.squareup.**
-dontwarn okio.**
-keep class java.nio.** { *; }
-keep class okio.** {*;}

#retrofit2  混淆
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

#解决报错：升级到 AGP 8.0 后 Retrofit 报错（java.lang.Class cannot be cast to java.lang.reflect.ParameterizedType）
#参考链接：https://github.com/square/retrofit/issues/3751#issuecomment-1192043644
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
#With R8 full mode generic signatures are stripped for classes that are not
#kept. Suspend functions are wrapped in continuations where the type argument
#is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

#Liveeventbus相关
-dontwarn com.jeremyliao.liveeventbus.**
-keep class com.jeremyliao.liveeventbus.** { *; }
-keep class androidx.lifecycle.** { *; }
-keep class androidx.arch.core.** { *; }

#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
# 如果你的API级别<=Android API 27 则需要添加
#-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# ===============================================================================
# 眼动追踪模块JNI保护 (核心功能模块)
# ===============================================================================

# JNI基础保护 - 所有native方法（已在基础组件保护中定义）

# 眼动追踪JNI桥接类 - 与C++层通信的核心类
-keep class com.airdoc.mpd.gaze.track.GazeTrack {
    *;
}

# JNI回调类保护 - C++层会直接调用这些类
-keep class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    *;
}
-keep class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    *;
}

# JNI回调方法签名保护 - 确保C++层能正确找到回调方法
-keepclassmembers class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    public void onGazeServiceModeChange(int);
    public void onCalibrateCoordinate(boolean, boolean, float, int, int, float, float, int);
}
-keepclassmembers class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    public void onGazeAppliedModeChange(int);
}

# 眼动追踪服务类保护
-keep class com.airdoc.mpd.gaze.track.GazeTrackService {
    *;
}
-keep class com.airdoc.mpd.gaze.track.TrackingManager {
    *;
}
-keep class com.airdoc.mpd.gaze.GazeTrackingManager {
    *;
}

# ===============================================================================
# 数据模型保护 (网络API和本地存储)
# ===============================================================================

# 眼动追踪数据模型 - Gson序列化/反序列化
-keep class com.airdoc.mpd.gaze.bean.** {
    *;
}
-keep class com.airdoc.mpd.gaze.enumeration.** {
    *;
}

# 设备管理数据模型
-keep class com.airdoc.mpd.device.bean.** {
    *;
}

# 用户管理数据模型
-keep class com.airdoc.mpd.user.bean.** {
    *;
}

# 应用更新数据模型
-keep class com.airdoc.mpd.update.bean.** {
    *;
}

# 媒体播放数据模型
-keep class com.airdoc.mpd.media.bean.** {
    *;
}

# 检测相关数据模型
-keep class com.airdoc.mpd.detection.bean.** {
    *;
}

# ===============================================================================
# 相机和人脸检测模块保护
# ===============================================================================

# CameraX相关保护
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# 人脸检测相关保护 (MLKit)
-keep class com.google.mlkit.** { *; }
-dontwarn com.google.mlkit.**

# 自定义相机管理器
-keep class com.airdoc.mpd.gaze.camera.GTCameraManager {
    *;
}
-keep class com.airdoc.mpd.gaze.camera.ICameraListener {
    *;
}

# 人脸检测处理器
-keep class com.airdoc.mpd.face.FaceDetectorProcessor {
    *;
}

# ===============================================================================
# 眼动应用和数据上传模块保护
# ===============================================================================

# 眼动应用JNI类
-keep class com.airdoc.mpd.gaze.application.GazeApplied {
    *;
}

# 数据上传JNI类
-keep class com.airdoc.mpd.gaze.upload.UploadCloud {
    *;
}

# ===============================================================================
# 第三方库保护
# ===============================================================================

# MPAndroidChart - 图表显示库
-dontwarn com.github.mikephil.**
-keep class com.github.mikephil.**{ *; }

# ExoPlayer - 媒体播放库 (多语言音频播放)
-keep class com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**

# WebSocket - 实时数据通信
-keep class org.java_websocket.** { *; }
-dontwarn org.java_websocket.**

# MMKV - 高性能键值存储
-keep class com.tencent.mmkv.** { *; }
-dontwarn com.tencent.mmkv.**

# Lottie - 动画库
-keep class com.airbnb.lottie.** { *; }
-dontwarn com.airbnb.lottie.**

# ===============================================================================
# MQTT和云端通信保护 (数据上传模块)
# ===============================================================================

# 阿里云MQTT SDK
-keep class com.aliyun.alink.**{*;}
-keep class com.aliyun.linksdk.**{*;}
-dontwarn com.aliyun.**
-dontwarn com.alibaba.**
-dontwarn com.alipay.**
-dontwarn com.ut.**

# Netty网络框架 (MQTT底层依赖)
-keepattributes Signature,InnerClasses
-keepclasseswithmembers class io.netty.** {
    *;
}
-keepnames class io.netty.** {
    *;
}
-dontwarn io.netty.**
-dontwarn sun.**

# Eclipse Paho MQTT客户端
-keep public class org.eclipse.paho.**{*;}

# JSON处理库保护
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.**{*;}
-keep class com.google.gson.** { *;}

# ===============================================================================
# 网络请求库保护 (API通信模块)
# ===============================================================================

# OkHttp和Retrofit - 网络请求核心库
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-dontwarn org.mozilla.**
-keep class okio.**{*;}
-keep class okhttp3.**{*;}
-keep class retrofit2.** { *; }
-dontwarn retrofit2.**

# 网络核心组件
-keep class org.apache.commons.codec.**{*;}

# 文件提供者和系统组件（如果存在）
-keep class android.support.**{*;}
-keep class android.os.**{*;}

# ===============================================================================
# 公共组件库保护 (LibCommon)
# ===============================================================================

# 网络实体类和统计事件类（如果存在LibCommon库）
# -keep class com.airdoc.component.common.net.entity.** {
#     *;
# }
# -keep class com.airdoc.component.common.stat.core.event.bean.** {
#     *;
# }

# 公共组件核心类（如果存在）
# -keep class com.airdoc.component.common.** {
#     public *;
# }

# ===============================================================================
# 蓝牙设备通信保护 (LePu血氧设备)
# ===============================================================================

# LePu蓝牙协议库 - 血氧设备通信
-keep class com.lepu.blepro.ext.**{*;}
-keep class com.lepu.blepro.constants.**{*;}
-keep class com.lepu.blepro.event.**{*;}
-keep class com.lepu.blepro.objs.**{*;}
-keep class com.lepu.blepro.utils.DateUtil{*;}
-keep class com.lepu.blepro.utils.HexString{*;}
-keep class com.lepu.blepro.utils.StringUtilsKt{*;}
-keep class com.lepu.blepro.utils.DecompressUtil{*;}
-keep class com.lepu.blepro.utils.FilterUtil{*;}
-keep class com.lepu.blepro.observer.**{*;}

# Stream Log库 (LePu依赖)
-keep class io.getstream.log.**{*;}
-dontwarn io.getstream.log.**

# ===============================================================================
# 应用管理器和工具类保护
# ===============================================================================

# 设备管理器
-keep class com.airdoc.mpd.device.DeviceManager {
    public *;
}

# 用户管理器
-keep class com.airdoc.mpd.user.UserManager {
    public *;
}

# 媒体播放管理器
-keep class com.airdoc.mpd.media.PlayManager {
    public *;
}

# 素材管理器
-keep class com.airdoc.mpd.detection.MaterialManager {
    public *;
}

# ===============================================================================
# 反射和序列化保护
# ===============================================================================

# 保护所有实现Serializable接口的类
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保护Gson反射使用的字段
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# ===============================================================================
# 性能优化和安全配置
# ===============================================================================

# 移除日志调用以提高性能（生产环境）
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除自定义Logger的调试日志（保留错误日志）
# 注意：只有在确认Logger类存在时才启用此规则
# -assumenosideeffects class com.airdoc.component.common.log.Logger {
#     public static void v(...);
#     public static void d(...);
#     public static void i(...);
# }

# ===============================================================================
# 医疗应用特殊保护
# ===============================================================================

# 保护校准相关的关键类（医疗精度要求）
-keep class com.airdoc.mpd.gaze.calibration.** {
    *;
}

# 保护检测算法相关类
-keep class com.airdoc.mpd.detection.DetectionActivity* {
    *;
}

# 保护HRV心率变异性检测相关类
-keep class com.airdoc.mpd.detection.hrv.** {
    *;
}

# ===============================================================================
# 配置文件结束
# ===============================================================================
