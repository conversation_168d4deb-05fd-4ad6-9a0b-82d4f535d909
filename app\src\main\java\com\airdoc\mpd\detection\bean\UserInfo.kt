package com.airdoc.mpd.detection.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class UserInfo(
    val id: Long,
    val orgId: String,
    val patientId: String,
    val username: String,
    val nickname: String?,
    val gender: String,
    val phone: String,
    val age: Int,
    val avatar: String?,
    val clientType: String,
    val clientId: String?
): Parcelable