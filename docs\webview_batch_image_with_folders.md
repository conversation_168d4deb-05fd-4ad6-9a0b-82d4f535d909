# WebView批量图片获取 - 素材库数字文件夹支持

## 📁 功能概述

WebView资源拦截的批量图片获取功能专门支持素材库中的数字文件夹结构。系统可以正确处理存储在不同数字文件夹中的素材库图片资源。

## 🏗️ 素材库目录结构

### 支持的素材库目录结构
```
/data/data/com.airdoc.mpd/files/materials/
├── 123/                    # 数字文件夹 - 素材库ID
│   ├── eye_chart.jpg
│   ├── color_test.png
│   └── vision_test.gif
├── 456/                    # 另一个数字文件夹 - 素材库ID
│   ├── reading_test.jpg
│   └── contrast_test.png
└── 789/                    # 第三个数字文件夹 - 素材库ID
    └── depth_test.jpg
```

## 🔗 URL映射规则

| 图片类型 | 前端请求URL | 对应imageId | 物理路径 |
|---------|------------|-------------|----------|
| 素材库图片 | `/material/123/eye_chart.jpg` | `material_123/eye_chart.jpg` | `/materials/123/eye_chart.jpg` |
| 素材库图片 | `/material/456/color_test.png` | `material_456/color_test.png` | `/materials/456/color_test.png` |
| 素材库图片 | `/material/789/depth_test.jpg` | `material_789/depth_test.jpg` | `/materials/789/depth_test.jpg` |

## 📝 前端使用方法

### 1. 基本用法

```javascript
// 请求不同素材库数字文件夹中的图片
const imageIds = [
    'material_123/eye_chart.jpg',      // 素材库123文件夹
    'material_456/color_test.png',     // 素材库456文件夹
    'material_789/vision_test.gif'     // 素材库789文件夹
];

// 获取图片URL列表
const imageListJson = android.getBatchImageUrls(JSON.stringify(imageIds));
const imageList = JSON.parse(imageListJson);

if (imageList.success) {
    console.log(`成功获取${imageList.count}个素材库图片URL`);
    // 返回的URL格式：
    // [
    //   { id: "material_123/eye_chart.jpg", url: "/material/123/eye_chart.jpg" },
    //   { id: "material_456/color_test.png", url: "/material/456/color_test.png" },
    //   { id: "material_789/vision_test.gif", url: "/material/789/vision_test.gif" }
    // ]
}
```

### 2. 完整的批量加载示例

```javascript
async function loadImagesFromDifferentMaterialFolders() {
    // 定义来自不同素材库数字文件夹的图片
    const imageIds = [
        // 素材库123中的图片
        'material_123/eye_chart.jpg',
        'material_123/color_wheel.png',

        // 素材库456中的图片
        'material_456/reading_test.jpg',
        'material_456/contrast_test.png',

        // 素材库789中的图片
        'material_789/depth_test.jpg',
        'material_789/vision_test.gif'
    ];
    
    try {
        // 1. 获取所有图片的URL列表
        const imageListJson = android.getBatchImageUrls(JSON.stringify(imageIds));
        const imageList = JSON.parse(imageListJson);
        
        if (!imageList.success) {
            throw new Error(imageList.error);
        }
        
        console.log(`准备从${imageList.count}个不同素材库文件夹加载图片`);

        // 2. 按素材库文件夹分组显示
        const materialGroups = {};
        imageList.urls.forEach(imageInfo => {
            const materialId = imageInfo.id.split('/')[0].replace('material_', '');
            if (!materialGroups[materialId]) {
                materialGroups[materialId] = [];
            }
            materialGroups[materialId].push(imageInfo);
        });
        
        // 3. 并发加载所有图片
        const loadPromises = imageList.urls.map(imageInfo => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    console.log(`✅ 图片加载成功: ${imageInfo.id}`);
                    resolve({
                        id: imageInfo.id,
                        url: imageInfo.url,
                        element: img,
                        materialId: imageInfo.id.split('/')[0].replace('material_', '')
                    });
                };
                img.onerror = () => {
                    console.error(`❌ 图片加载失败: ${imageInfo.id}`);
                    reject(new Error(`Failed to load: ${imageInfo.id}`));
                };
                
                // 设置图片源，触发shouldInterceptRequest拦截
                img.src = imageInfo.url;
            });
        });
        
        // 4. 等待所有图片加载完成
        const results = await Promise.all(loadPromises);
        
        // 5. 按素材库分组显示图片
        const container = document.getElementById('imageContainer');

        Object.keys(materialGroups).forEach(materialId => {
            // 创建素材库标题
            const materialTitle = document.createElement('h3');
            materialTitle.textContent = `素材库: ${materialId}`;
            container.appendChild(materialTitle);

            // 创建素材库容器
            const materialDiv = document.createElement('div');
            materialDiv.style.border = '1px solid #ccc';
            materialDiv.style.margin = '10px';
            materialDiv.style.padding = '10px';

            // 添加该素材库的所有图片
            results
                .filter(result => result.materialId === materialId)
                .forEach(result => {
                    result.element.style.width = '150px';
                    result.element.style.margin = '5px';
                    result.element.title = result.id;
                    materialDiv.appendChild(result.element);
                });

            container.appendChild(materialDiv);
        });

        console.log('🎉 所有素材库图片加载完成!');
        
    } catch (error) {
        console.error('❌ 批量加载素材库图片失败:', error);
    }
}
```

### 3. 动态素材库图片加载

```javascript
// 动态加载指定素材库的所有图片
async function loadMaterialLibrary(materialId) {
    try {
        // 假设我们知道该素材库中的图片列表
        const materialImages = [
            `material_${materialId}/image1.jpg`,
            `material_${materialId}/image2.png`,
            `material_${materialId}/image3.gif`
        ];
        
        const imageListJson = android.getBatchImageUrls(JSON.stringify(folderImages));
        const imageList = JSON.parse(imageListJson);
        
        if (imageList.success) {
            console.log(`素材库${materialId}中有${imageList.count}个图片`);
            
            // 加载图片...
            const loadPromises = imageList.urls.map(imageInfo => {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => resolve(img);
                    img.onerror = () => resolve(null);
                    img.src = imageInfo.url;
                });
            });
            
            const images = await Promise.all(loadPromises);
            const validImages = images.filter(img => img !== null);
            
            console.log(`成功加载${validImages.length}个图片`);
            return validImages;
        }
    } catch (error) {
        console.error(`加载素材库${materialId}失败:`, error);
        return [];
    }
}

// 使用示例
loadMaterialFolder(123).then(images => {
    images.forEach(img => {
        document.body.appendChild(img);
    });
});
```

## ⚡ 性能优化建议

1. **分批加载**: 对于大量图片，建议分批次加载
2. **预加载**: 可以预先获取URL列表，按需加载图片
3. **缓存策略**: 利用浏览器缓存，避免重复加载
4. **错误处理**: 对加载失败的图片进行重试或跳过

## 🔧 错误处理

```javascript
// 处理文件夹不存在的情况
const imageIds = ['material_999/nonexistent.jpg'];
const imageListJson = android.getBatchImageUrls(JSON.stringify(imageIds));
const imageList = JSON.parse(imageListJson);

if (imageList.success && imageList.count === 0) {
    console.warn('指定文件夹中没有找到图片');
} else if (!imageList.success) {
    console.error('获取图片列表失败:', imageList.error);
}
```

## 📋 总结

✅ **完全支持数字文件夹**：可以处理任意数字命名的文件夹结构
✅ **路径自动映射**：前端URL自动映射到正确的物理路径  
✅ **批量高效加载**：支持跨文件夹的批量图片加载
✅ **错误处理完善**：提供详细的错误信息和处理机制
✅ **性能优化**：利用浏览器并发能力和缓存机制
