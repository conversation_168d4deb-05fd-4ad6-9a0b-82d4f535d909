package com.airdoc.mpd.detection.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.mpd.detection.bean.SignalResult
import com.airdoc.mpd.detection.repository.SignalResultRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * 信号结果ViewModel
 */
class SignalResultViewModel : ViewModel() {

    private val TAG = SignalResultViewModel::class.java.simpleName

    private val signalResultRepository by lazy { SignalResultRepository() }

    // 上传结果LiveData
    val uploadResultLiveData = MutableLiveData<Boolean>()

    /**
     * 上传信号结果数据
     * @param signalResult 信号结果数据
     */
    fun uploadSignalResult(signalResult: SignalResult) {
        viewModelScope.launch {
            Logger.d(TAG, msg = "开始上传信号结果数据")
            
            MutableStateFlow(signalResultRepository.uploadSignalResult(signalResult)).collectResponse {
                onSuccess = { _, _, _ ->
                    Logger.d(TAG, msg = "信号结果数据上传成功")
                    uploadResultLiveData.postValue(true)
                }
                onDataEmpty = { _, _ ->
                    Logger.w(TAG, msg = "信号结果数据上传返回空数据")
                    uploadResultLiveData.postValue(true) // 认为上传成功
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "信号结果数据上传失败: errorCode=$errorCode, errorMsg=$errorMsg")
                    uploadResultLiveData.postValue(false)
                }
                onError = { throwable ->
                    Logger.e(TAG, msg = "信号结果数据上传异常: ${throwable?.message}")
                    uploadResultLiveData.postValue(false)
                }
            }
        }
    }
}
