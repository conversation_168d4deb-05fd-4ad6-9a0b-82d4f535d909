# 批量素材库下载功能测试说明

## 功能概述
更新了测试集版本修改代码，现在支持遍历所有测试机（素材库）列表，并为每个都进行下载解压操作。

## 主要修改

### 1. MaterialViewModel.kt 修改
- **修改了 `listMaterialLibraries()` 方法**：从只处理第一个素材库改为调用 `downloadAllMaterials()` 处理所有素材库
- **新增 `downloadAllMaterials()` 方法**：筛选需要下载的素材库（本地不存在的），并开始批量下载流程
- **新增 `downloadNextMaterial()` 方法**：按顺序下载下一个素材库
- **修改了 `downloadMaterial()` 方法**：增加了批量下载的进度显示和状态管理
- **新增批量下载状态跟踪变量**：
  - `pendingDownloads`: 待下载的素材库列表
  - `currentDownloadIndex`: 当前下载索引
  - `totalDownloadCount`: 总下载数量

### 2. MoreSettingsActivity.kt 修改
- **修改了 `initObserver()` 方法**：更新了素材库更新和下载完成的处理逻辑
- **新增 `generateVersionInfo()` 方法**：基于所有素材库生成综合版本信息
- **新增 `parseVersionNumber()` 方法**：解析版本号用于版本比较
- **改进了下载状态监听**：减少不必要的Toast弹窗，只在重要状态变化时显示

## 功能特点

### 批量下载流程
1. 获取所有素材库列表
2. 筛选出本地不存在的素材库
3. 按顺序逐个下载和解压
4. 显示下载进度（如：1/3, 2/3, 3/3）
5. 所有下载完成后更新版本信息

### 版本信息生成策略
- **单个素材库**：直接使用该素材库的版本号
- **多个相同版本**：使用统一版本号
- **多个不同版本**：使用最新版本号 + 素材库数量标识（如：V2.1.0+3库）

### 状态监听优化
- 下载状态实时记录到日志
- 只在关键状态变化时显示Toast（开始下载、解压完成、错误）
- 支持下载进度监听

## 测试建议

### 测试场景
1. **空素材库列表**：验证默认版本号处理
2. **单个素材库**：验证单库下载流程
3. **多个素材库（全新）**：验证批量下载流程
4. **部分已存在**：验证增量下载逻辑
5. **网络异常**：验证错误处理机制

### 验证点
- [ ] 素材库列表获取成功
- [ ] 正确筛选需要下载的素材库
- [ ] 按顺序下载每个素材库
- [ ] 下载进度正确显示
- [ ] 解压过程正常完成
- [ ] 版本信息正确生成和保存
- [ ] UI状态更新正确
- [ ] 错误情况处理正常

## 日志关键字
监控以下日志关键字来跟踪下载过程：
- `开始批量下载素材库`
- `开始下载第X/Y个素材库`
- `所有素材库下载完成`
- `素材库版本更新成功`
- `下载状态:`
- `下载进度:`

## 注意事项
1. 下载过程是串行的，避免并发下载导致的资源竞争
2. 每个素材库下载完成后会立即解压，确保数据完整性
3. 版本信息会在所有下载完成后统一更新
4. 支持下载过程中的错误恢复机制
