package com.airdoc.mpd.detection

import android.content.Context
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.detection.bean.MaterialLibraryDto
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.security.MessageDigest
import java.util.concurrent.ConcurrentHashMap

/**
 * FileName: MaterialManager
 * Author by <PERSON><PERSON><PERSON><PERSON><PERSON>,Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 素材库管理器 - 负责素材库的下载、解压、存储和映射管理
 */
object MaterialManager {

    private const val TAG = "MaterialManager"
    
    // 素材库根目录名
    private const val MATERIAL_ROOT_DIR = "materials"
    
    // 素材库映射文件名
    private const val MATERIAL_MAPPING_FILE = "material_mapping.json"
    
    private val gson = Gson()
    
    // 内存中的素材库映射缓存
    private val materialMappingCache = ConcurrentHashMap<Long, MaterialInfo>()

    /**
     * 素材库信息
     */
    data class MaterialInfo(
        val id: Long,
        val name: String,
        val description: String,
        val version: String,
        val localPath: String,
        val downloadTime: Long,
        val fileCount: Int,
        val checksum: String
    )

    /**
     * 获取素材库根目录
     */
    fun getMaterialRootDir(context: Context): File {
        val rootDir = File(context.filesDir, MATERIAL_ROOT_DIR)
        if (!rootDir.exists()) {
            rootDir.mkdirs()
        }
        return rootDir
    }

    /**
     * 获取指定素材库的目录
     */
    fun getMaterialDir(context: Context, materialId: Long): File {
        val materialDir = File(getMaterialRootDir(context), materialId.toString())
        if (!materialDir.exists()) {
            materialDir.mkdirs()
        }
        return materialDir
    }

    /**
     * 获取素材库映射文件
     */
    private fun getMaterialMappingFile(context: Context): File {
        return File(getMaterialRootDir(context), MATERIAL_MAPPING_FILE)
    }

    /**
     * 加载素材库映射
     */
    fun loadMaterialMapping(context: Context) {
        try {
            val mappingFile = getMaterialMappingFile(context)
            if (mappingFile.exists()) {
                val reader = FileReader(mappingFile)
                val type = object : TypeToken<Map<String, MaterialInfo>>() {}.type
                val mapping: Map<String, MaterialInfo> = gson.fromJson(reader, type) ?: emptyMap()
                reader.close()
                
                materialMappingCache.clear()
                mapping.forEach { (key, value) ->
                    materialMappingCache[key.toLong()] = value
                }
                Logger.d(TAG, msg = "加载素材库映射成功，共${materialMappingCache.size}个素材库")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "加载素材库映射失败: ${e.message}")
        }
    }

    /**
     * 保存素材库映射
     */
    private fun saveMaterialMapping(context: Context) {
        try {
            val mappingFile = getMaterialMappingFile(context)
            val writer = FileWriter(mappingFile)
            
            // 转换为String key的Map以便JSON序列化
            val mapping = materialMappingCache.mapKeys { it.key.toString() }
            gson.toJson(mapping, writer)
            writer.close()
            Logger.d(TAG, msg = "保存素材库映射成功")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "保存素材库映射失败: ${e.message}")
        }
    }

    /**
     * 添加或更新素材库信息
     */
    fun addOrUpdateMaterial(context: Context, materialLibrary: MaterialLibraryDto, localPath: String, fileCount: Int) {
        val materialInfo = MaterialInfo(
            id = materialLibrary.id ?: 0L,
            name = materialLibrary.name ?: "",
            description = materialLibrary.description ?: "",
            version = materialLibrary.version ?: "",
            localPath = localPath,
            downloadTime = System.currentTimeMillis(),
            fileCount = fileCount,
            checksum = materialLibrary.checksum ?: ""
        )
        
        materialMappingCache[materialInfo.id] = materialInfo
        saveMaterialMapping(context)
        Logger.d(TAG, msg = "添加素材库信息: ${materialInfo.name} (${materialInfo.version})")
    }

    /**
     * 获取素材库信息
     */
    fun getMaterialInfo(materialId: Long): MaterialInfo? {
        return materialMappingCache[materialId]
    }

    /**
     * 获取所有素材库信息
     */
    fun getAllMaterialInfo(): List<MaterialInfo> {
        return materialMappingCache.values.toList()
    }

    /**
     * 检查素材库是否存在
     */
    fun isMaterialExists(context: Context, materialId: Long): Boolean {
        val materialDir = getMaterialDir(context, materialId)
        return materialDir.exists() && materialDir.listFiles()?.isNotEmpty() == true
    }

    /**
     * 获取素材库中的所有图片文件
     */
    fun getMaterialImages(context: Context, materialId: Long): List<File> {
        val materialDir = getMaterialDir(context, materialId)
        if (!materialDir.exists()) {
            return emptyList()
        }
        
        return materialDir.listFiles { file ->
            file.isFile && isImageFile(file.name)
        }?.toList() ?: emptyList()
    }

    /**
     * 判断是否为图片文件
     */
    private fun isImageFile(fileName: String): Boolean {
        val imageExtensions = arrayOf(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp")
        val lowerCaseName = fileName.lowercase()
        return imageExtensions.any { lowerCaseName.endsWith(it) }
    }

    /**
     * 删除素材库
     */
    fun deleteMaterial(context: Context, materialId: Long): Boolean {
        return try {
            val materialDir = getMaterialDir(context, materialId)
            val deleted = materialDir.deleteRecursively()
            if (deleted) {
                materialMappingCache.remove(materialId)
                saveMaterialMapping(context)
                Logger.d(TAG, msg = "删除素材库成功: $materialId")
            }
            deleted
        } catch (e: Exception) {
            Logger.e(TAG, msg = "删除素材库失败: ${e.message}")
            false
        }
    }

    /**
     * 计算文件SHA256校验码
     */
    fun calculateSHA256(file: File): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            file.inputStream().use { input ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算SHA256失败: ${e.message}")
            ""
        }
    }

    /**
     * 验证文件完整性
     */
    fun verifyFileIntegrity(file: File, expectedChecksum: String): Boolean {
        if (expectedChecksum.isEmpty()) return true
        val actualChecksum = calculateSHA256(file)
        return actualChecksum.equals(expectedChecksum, ignoreCase = true)
    }
}
